import io
import os

from fastapi import (
    Depends,
    FastAPI,
    File,
    Form,
    HTTPException,
    UploadFile,
    status,
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.staticfiles import StaticFiles
import pandas as pd
from sqlmodel import Session

from app import clean_merge_hisa, reconcile_logs
from app.airtel import prepare_airtel_telco
from app.auth import (
    authenticate_user,
    generate_tokens,
    get_current_user,
)
from app.database import get_session
from app.glo import prepare_glo_telco
from app.mtn import prepare_mtn_telco
from app.config import BASE_DIR


app = FastAPI(
    title="Hisa Reconciliation Manager",
    max_request_size=100 * 1024 * 1024  # 100MB limit
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount the reports directory for static file serving
app.mount("/reports", StaticFiles(directory=os.path.join(BASE_DIR, "reports")), name="reports")


@app.get("/")
def home():
    return {"message": "Welcome to Hisa Reconciliation Manager"}


@app.post("/login/", status_code=status.HTTP_200_OK)
def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    session: Session = Depends(get_session),
):
    user = authenticate_user(form_data.username, form_data.password, session)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    tokens = generate_tokens(data={
        "user": user.email,
        "user_id": str(user.uid)
    })
    return {"data": tokens}


@app.post("/reconcile_logs/")
async def reconciliation_manager(
    mno: str = Form(...),
    target_date: str = Form(...),
    hisa_file: UploadFile = File(...),
    hisa_file2: UploadFile = File(...),
    telco_file: UploadFile = File(...),
    telco_file2: UploadFile = File(None),
    use_transaction_id: bool = Form(False),
    user: dict = Depends(get_current_user),
):
    try:
        hisa_content = await hisa_file.read()
        hisa_dfs = [clean_merge_hisa(mno.upper(), "hisa1", io.BytesIO(hisa_content))]
        hisa_content2 = await hisa_file2.read()
        hisa_dfs.append(clean_merge_hisa(mno.upper(), "hisa2", io.BytesIO(hisa_content2)))
        hisa_df = pd.concat(hisa_dfs) if len(hisa_dfs) > 1 else hisa_dfs[0]
        
        # Process Telco files
        if mno.upper() == "MTN":
            mtn_content = await telco_file.read()
            mtn_dfs = [pd.read_excel(io.BytesIO(mtn_content))]

            if telco_file2:
                mtn_content2 = await telco_file2.read()
                mtn_dfs.append(pd.read_excel(io.BytesIO(mtn_content2)))

            mtn_df = pd.concat(mtn_dfs) if len(mtn_dfs) > 1 else mtn_dfs[0]
            cleaned_telco = prepare_mtn_telco(mtn_df)
            result = reconcile_logs(
                mno=mno.upper(),
                target_date=target_date,
                hisa_admin=hisa_df,
                telco_df=cleaned_telco,
                use_transaction_id=use_transaction_id
            )
            response_data = {
                "hisa_all_count": result["hisa_all_count"],
                "hisa_all_value": result["hisa_all_value"],
                "telco_all_count": result["telco_all_count"],
                "telco_all_value": result["telco_all_value"],
                "hisa_success_count": result["hisa_success_count"],
                "telco_success_count": result["telco_success_count"],
                "matching_statistics": result["matching_statistics"],
                "hisa_total": result["hisa_total"],
                "telco_total": result["telco_total"],
                "difference": result["difference"],
                "missing_in_hisa_count": len(result["missing_in_hisa"]),
                "extra_in_hisa_count": len(result["extra_in_hisa"]),
                "local_duplicates_count": len(result["local_duplicates"]),
                "telco_duplicates_count": len(result["telco_duplicates"]),
                "duplicates_file_url": f"/reports/{mno.upper()}_reconciliation/duplicates_report/{os.path.basename(result['duplicates_file'])}",
                "missing_file_url": f"/reports/{mno.upper()}_reconciliation/missing_report/{os.path.basename(result['missing_file'])}",
                "user_consumption_file_url": f"/reports/{mno.upper()}_reconciliation/user_consumption_report/{os.path.basename(result['user_consumption_file'])}",
                # New fields
                "hisa_airtime_count": result["hisa_airtime_count"],
                "hisa_airtime_value": result["hisa_airtime_value"],
                "hisa_data_count": result["hisa_data_count"],
                "hisa_data_value": result["hisa_data_value"],
                "telco_airtime_count": result["telco_airtime_count"],
                "telco_airtime_value": result["telco_airtime_value"],
                "telco_data_count": result["telco_data_count"],
                "telco_data_value": result["telco_data_value"],
                "hisa_failed_count": result["hisa_failed_count"],
                "hisa_failed_value": result["hisa_failed_value"],
                "telco_failed_count": result["telco_failed_count"],
                "telco_failed_value": result["telco_failed_value"],
                "hisa_success_airtime_count": result["hisa_success_airtime_count"],
                "hisa_success_airtime_value": result["hisa_success_airtime_value"],
                "hisa_success_data_count": result["hisa_success_data_count"],
                "hisa_success_data_value": result["hisa_success_data_value"],
                "telco_success_airtime_count": result["telco_success_airtime_count"],
                "telco_success_airtime_value": result["telco_success_airtime_value"],
                "telco_success_data_count": result["telco_success_data_count"],
                "telco_success_data_value": result["telco_success_data_value"]
            }
        if mno.upper() == "AIRTEL":
            airtel_content = await telco_file.read()
            airtel_dfs = [pd.read_excel(io.BytesIO(airtel_content), dtype=str)]

            if telco_file2:
                airtel_content2 = await telco_file2.read()
                airtel_dfs.append(pd.read_excel(io.BytesIO(airtel_content2), dtype=str))

            airtel_df = pd.concat(airtel_dfs) if len(airtel_dfs) > 1 else airtel_dfs[0]
            cleaned_telco = prepare_airtel_telco(airtel_df)
            result = reconcile_logs(
                mno="AIRTEL",
                target_date=target_date,
                hisa_admin=hisa_df,
                telco_df=cleaned_telco,
                use_transaction_id=use_transaction_id
            )
            response_data = {
                "hisa_all_count": result["hisa_all_count"],
                "hisa_all_value": result["hisa_all_value"],
                "telco_all_count": result["telco_all_count"],
                "telco_all_value": result["telco_all_value"],
                "hisa_success_count": result["hisa_success_count"],
                "telco_success_count": result["telco_success_count"],
                "matching_statistics": result["matching_statistics"],
                "hisa_total": result["hisa_total"],
                "telco_total": result["telco_total"],
                "difference": result["difference"],
                "missing_in_hisa_count": len(result["missing_in_hisa"]),
                "extra_in_hisa_count": len(result["extra_in_hisa"]),
                "local_duplicates_count": len(result["local_duplicates"]),
                "telco_duplicates_count": len(result["telco_duplicates"]),
                "duplicates_file_url": f"/reports/{mno.upper()}_reconciliation/duplicates_report/{os.path.basename(result['duplicates_file'])}",
                "missing_file_url": f"/reports/{mno.upper()}_reconciliation/missing_report/{os.path.basename(result['missing_file'])}",
                "user_consumption_file_url": f"/reports/{mno.upper()}_reconciliation/user_consumption_report/{os.path.basename(result['user_consumption_file'])}",
                # New fields
                "hisa_airtime_count": result["hisa_airtime_count"],
                "hisa_airtime_value": result["hisa_airtime_value"],
                "hisa_data_count": result["hisa_data_count"],
                "hisa_data_value": result["hisa_data_value"],
                "telco_airtime_count": result["telco_airtime_count"],
                "telco_airtime_value": result["telco_airtime_value"],
                "telco_data_count": result["telco_data_count"],
                "telco_data_value": result["telco_data_value"],
                "hisa_failed_count": result["hisa_failed_count"],
                "hisa_failed_value": result["hisa_failed_value"],
                "telco_failed_count": result["telco_failed_count"],
                "telco_failed_value": result["telco_failed_value"],
                "hisa_success_airtime_count": result["hisa_success_airtime_count"],
                "hisa_success_airtime_value": result["hisa_success_airtime_value"],
                "hisa_success_data_count": result["hisa_success_data_count"],
                "hisa_success_data_value": result["hisa_success_data_value"],
                "telco_success_airtime_count": result["telco_success_airtime_count"],
                "telco_success_airtime_value": result["telco_success_airtime_value"],
                "telco_success_data_count": result["telco_success_data_count"],
                "telco_success_data_value": result["telco_success_data_value"]
            }
        if mno.upper() == "GLO":
            glo_content = await telco_file.read()
            glo_dfs = [pd.read_excel(io.BytesIO(glo_content))]

            if telco_file2:
                glo_content2 = await telco_file2.read()
                glo_dfs.append(pd.read_excel(io.BytesIO(glo_content2)))

            glo_df = pd.concat(glo_dfs) if len(glo_dfs) > 1 else glo_dfs[0]
            cleaned_telco = prepare_glo_telco(glo_df)
            result = reconcile_logs(
                mno="GLO",
                target_date=target_date,
                hisa_admin=hisa_df,
                telco_df=cleaned_telco,
                use_transaction_id=use_transaction_id
            )
            response_data = {
                "hisa_all_count": result["hisa_all_count"],
                "hisa_all_value": result["hisa_all_value"],
                "telco_all_count": result["telco_all_count"],
                "telco_all_value": result["telco_all_value"],
                "hisa_success_count": result["hisa_success_count"],
                "telco_success_count": result["telco_success_count"],
                "matching_statistics": result["matching_statistics"],
                "hisa_total": result["hisa_total"],
                "telco_total": result["telco_total"],
                "difference": result["difference"],
                "missing_in_hisa_count": len(result["missing_in_hisa"]),
                "extra_in_hisa_count": len(result["extra_in_hisa"]),
                "local_duplicates_count": len(result["local_duplicates"]),
                "telco_duplicates_count": len(result["telco_duplicates"]),
                "duplicates_file_url": f"/reports/{mno.upper()}_reconciliation/duplicates_report/{os.path.basename(result['duplicates_file'])}",
                "missing_file_url": f"/reports/{mno.upper()}_reconciliation/missing_report/{os.path.basename(result['missing_file'])}",
                "user_consumption_file_url": f"/reports/{mno.upper()}_reconciliation/user_consumption_report/{os.path.basename(result['user_consumption_file'])}",
                # New fields
                "hisa_airtime_count": result["hisa_airtime_count"],
                "hisa_airtime_value": result["hisa_airtime_value"],
                "hisa_data_count": result["hisa_data_count"],
                "hisa_data_value": result["hisa_data_value"],
                "telco_airtime_count": result["telco_airtime_count"],
                "telco_airtime_value": result["telco_airtime_value"],
                "telco_data_count": result["telco_data_count"],
                "telco_data_value": result["telco_data_value"],
                "hisa_failed_count": result["hisa_failed_count"],
                "hisa_failed_value": result["hisa_failed_value"],
                "telco_failed_count": result["telco_failed_count"],
                "telco_failed_value": result["telco_failed_value"],
                "hisa_success_airtime_count": result["hisa_success_airtime_count"],
                "hisa_success_airtime_value": result["hisa_success_airtime_value"],
                "hisa_success_data_count": result["hisa_success_data_count"],
                "hisa_success_data_value": result["hisa_success_data_value"],
                "telco_success_airtime_count": result["telco_success_airtime_count"],
                "telco_success_airtime_value": result["telco_success_airtime_value"],
                "telco_success_data_count": result["telco_success_data_count"],
                "telco_success_data_value": result["telco_success_data_value"]
            }
        return JSONResponse(content=response_data)
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": f"Error processing files: {str(e)}"}
        )
